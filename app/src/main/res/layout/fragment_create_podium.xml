<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.app.messej.data.model.enums.PodiumKind" />

        <import type="com.app.messej.data.model.enums.TheaterAudienceFee" />

        <import type="com.app.messej.data.model.enums.TheaterStageFee" />
        <import type="com.app.messej.data.model.enums.PodiumWhoCanJoin" />
        <import type="com.app.messej.data.model.enums.SpeakingJoiningFee" />


        <variable
            name="viewModel"
            type="com.app.messej.ui.home.publictab.podiums.create.CreatePodiumViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:liftOnScroll="true"
            tools:showIn="@layout/fragment_create_podium"
            tools:visibility="visible">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsing_toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?toolbarColor"
                android:fitsSystemWindows="true"
                app:contentScrim="@color/transparent"
                app:layout_scrollFlags="noScroll"
                app:statusBarScrim="@color/transparent"
                app:titleEnabled="false">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fitsSystemWindows="true"
                    app:layout_collapseMode="parallax"
                    app:layout_collapseParallaxMultiplier="1.0">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/customActionBarHeight"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:layout_marginBottom="-8dp"
                        android:adjustViewBounds="true"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/bg_toolbar_circle_expanded_left" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/customActionBarHeight"
                        android:adjustViewBounds="true"
                        android:paddingBottom="@dimen/element_spacing"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/bg_toolbar_circle_top_right" />

                </androidx.constraintlayout.widget.ConstraintLayout>


                <com.google.android.material.appbar.MaterialToolbar
                    android:id="@+id/toolbar"
                    style="@style/Widget.Flashat.Toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/customActionBarHeight"
                    android:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:title="@{viewModel.editMode==false?@string/title_podium:viewModel.name}"
                    tools:title="Page Title" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <com.kennyc.view.MultiStateView
            android:id="@+id/multiStateView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:msv_loadingView="@layout/layout_create_huddle_eds_loading"
            app:msv_viewState="content">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:applySystemBarInsets="@{`ime|bottom`}">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_name"
                        style="@style/TextAppearance.Flashat.Label"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/double_margin"
                        android:text="@string/create_podium_label_name"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="@string/create_podium_label_name" />


                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/text_input_name"
                        style="@style/Widget.Flashat.GreyTextInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:autofillHints="name"
                        android:enabled="@{!viewModel.createPodiumLoading}"
                        android:importantForAutofill="yes"
                        app:hintAnimationEnabled="false"
                        app:counterEnabled="@{viewModel.isBirthdayPodium()?false:true}"
                        app:counterMaxLength="@{viewModel.isBirthdayPodium()?100:20}"
                        app:hintEnabled="false"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/label_name">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/text_input_huddle_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/enter_name"
                            android:inputType="textPersonName"
                            android:text="@={viewModel.name}" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_Select_Photo"
                        style="@style/TextAppearance.Flashat.Label"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:text="@string/create_podium_label_select_photo"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/text_input_name" />


                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/cardView_Select_Photo"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/element_spacing"
                        app:cardBackgroundColor="@color/textInputBackground"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/label_Select_Photo">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/huddleDpLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/textView_Select_Image"
                                style="@style/ThemeOverlay.Flashat.GreyTextInput"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:gravity="center_horizontal"
                                android:text="@string/text_view_create_podium_select_photo"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@id/camera"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <com.makeramen.roundedimageview.RoundedImageView
                                android:id="@+id/huddle_dp"
                                android:layout_width="@dimen/profile_header_dp_size"
                                android:layout_height="@dimen/profile_header_dp_size"
                                android:layout_marginHorizontal="3dp"
                                android:layout_marginVertical="3dp"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:clickable="true"
                                android:elevation="3dp"
                                android:foreground="?attr/selectableItemBackground"
                                android:scaleType="centerCrop"
                                app:imageUrl="@{viewModel.imageToDisplay}"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:placeholder="@{@drawable/im_podium_placeholder}"
                                app:riv_border_color="@color/colorDpBorder"
                                app:riv_border_width="3dp"
                                app:riv_corner_radius="3dp"
                                tools:riv_corner_radius="@dimen/premium_huddle_dp_radius"
                                tools:src="@drawable/im_podium_placeholder" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/camera"
                                android:layout_width="@dimen/drawer_header_premium_badge_size"
                                android:layout_height="@dimen/drawer_header_premium_badge_size"
                                android:layout_marginStart="-8dp"
                                android:elevation="4dp"
                                android:src="@drawable/ic_camera_circle"
                                app:layout_constraintBottom_toBottomOf="@id/huddle_dp"
                                app:layout_constraintStart_toStartOf="@id/huddle_dp"
                                app:layout_constraintTop_toTopOf="@id/huddle_dp" />

                        </androidx.constraintlayout.widget.ConstraintLayout>


                    </com.google.android.material.card.MaterialCardView>


                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_type"
                        style="@style/TextAppearance.Flashat.Label"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:text="@string/create_podium_label_podium_type"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/cardView_Select_Photo"
                        tools:text="Podium Privacy" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/type_dropdown"
                        style="@style/Widget.Flashat.GreyTextInput.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:enabled="@{!viewModel.createPodiumLoading &amp;&amp; viewModel.canEditPodiumType}"
                        app:hintAnimationEnabled="false"
                        app:hintEnabled="false"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/label_type"
                        tools:text="@string/create_podium_select_type">

                        <AutoCompleteTextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:dropDownAnchor="@id/type_dropdown"
                            android:dropDownWidth="wrap_content"
                            android:dropDownHeight="wrap_content"
                            android:hint="@string/create_podium_select_type"
                            android:inputType="none" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_Podium_kind"
                        style="@style/TextAppearance.Flashat.Label"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:text="@string/create_podium_label_kind"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/type_dropdown" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/kind_podium_dropdown"
                        style="@style/Widget.Flashat.GreyTextInput.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:enabled="@{!viewModel.createPodiumLoading &amp;&amp; viewModel.canEditPodiumKind}"
                        app:hintAnimationEnabled="false"
                        app:hintEnabled="false"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/label_Podium_kind"
                        tools:text="Select Type">

                        <AutoCompleteTextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:dropDownAnchor="@id/kind_podium_dropdown"
                            android:dropDownWidth="wrap_content"
                            android:dropDownHeight="wrap_content"
                            android:hint="@string/create_huddle_select_kind"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_kind_note"
                        style="@style/TextAppearance.Flashat.Label"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/kind_podium_dropdown"
                        tools:text="@string/create_podium_kind_note" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_who_can_join"
                        style="@style/TextAppearance.Flashat.Label"
                        goneIfNot="@{viewModel.whoCanJoinVisible}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:text="@string/podium_who_can_join"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/label_kind_note" />

                    <com.google.android.flexbox.FlexboxLayout
                        android:id="@+id/radioWhoCanJoin"
                        goneIfNot="@{viewModel.whoCanJoinVisible}"
                        android:layout_width="0dp"
                        app:flexWrap="wrap"
                        android:layout_height="wrap_content"
                        app:justifyContent="space_between"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/label_who_can_join">

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioBtnAnyOne"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            app:layout_flexBasisPercent="45%"
                            android:checked="@{viewModel.whoCanJoinType==PodiumWhoCanJoin.ANY_ONE_CAN}"
                            android:onClick="@{() -> viewModel.setWhoCanJoin(PodiumWhoCanJoin.ANY_ONE_CAN)}"
                            android:text="@string/podium_join_with_rating_anyone"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioAboveOrEqualNinety"
                            android:layout_width="0dp"
                            app:layout_flexBasisPercent="45%"
                            android:layout_height="wrap_content"
                            android:checked="@{viewModel.whoCanJoinType==PodiumWhoCanJoin.RATING_ABOVE_NINETY}"
                            android:onClick="@{() -> viewModel.setWhoCanJoin(PodiumWhoCanJoin.RATING_ABOVE_NINETY)}"
                            android:text="@string/podium_join_rating_above_ninety"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioBtnOnlyHundred"
                            android:layout_width="0dp"
                            app:layout_flexBasisPercent="100%"
                            android:layout_height="wrap_content"
                            android:checked="@{viewModel.whoCanJoinType==PodiumWhoCanJoin.RATING_ONLY_HUNDRED}"
                            android:onClick="@{() -> viewModel.setWhoCanJoin(PodiumWhoCanJoin.RATING_ONLY_HUNDRED)}"
                            android:text="@string/podium_join_only_hundred"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />
                    </com.google.android.flexbox.FlexboxLayout>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_who_can_comment"
                        style="@style/TextAppearance.Flashat.Label"
                        goneIfNot="@{viewModel.canCommentViewVisible}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:text="@string/podium_who_can_comment"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/radioWhoCanJoin" />

                    <com.google.android.flexbox.FlexboxLayout
                        android:id="@+id/flex_layout_comment"
                        goneIfNot="@{viewModel.canCommentViewVisible}"
                        android:layout_width="0dp"
                        app:flexWrap="wrap"
                        android:layout_height="wrap_content"
                        app:justifyContent="space_between"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/label_who_can_comment">

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioBtnCommentAnyOne"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            app:layout_flexBasisPercent="45%"
                            android:checked="@{viewModel.whoCanComment==PodiumWhoCanJoin.ANY_ONE_CAN}"
                            android:onClick="@{() -> viewModel.setWhoCanComment(PodiumWhoCanJoin.ANY_ONE_CAN)}"
                            android:text="@string/podium_join_with_rating_anyone"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioCommentAboveOrEqualNinety"
                            android:layout_width="0dp"
                            app:layout_flexBasisPercent="45%"
                            android:layout_height="wrap_content"
                            android:checked="@{viewModel.whoCanComment==PodiumWhoCanJoin.RATING_ABOVE_NINETY}"
                            android:onClick="@{() -> viewModel.setWhoCanComment(PodiumWhoCanJoin.RATING_ABOVE_NINETY)}"
                            android:text="@string/podium_join_rating_above_ninety"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioCommentBtnOnlyHundred"
                            android:layout_width="0dp"
                            app:layout_flexBasisPercent="100%"
                            android:layout_height="wrap_content"
                            android:checked="@{viewModel.whoCanComment==PodiumWhoCanJoin.RATING_ONLY_HUNDRED}"
                            android:onClick="@{() -> viewModel.setWhoCanComment(PodiumWhoCanJoin.RATING_ONLY_HUNDRED)}"
                            android:text="@string/podium_join_only_hundred"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />
                    </com.google.android.flexbox.FlexboxLayout>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_who_can_speak"
                        style="@style/TextAppearance.Flashat.Label"
                        goneIfNot="@{viewModel.canSpeakViewVisible}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:text="@string/podium_who_can_speak"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/flex_layout_comment" />

                    <com.google.android.flexbox.FlexboxLayout
                        android:id="@+id/flex_layout_speak"
                        goneIfNot="@{viewModel.canSpeakViewVisible}"
                        android:layout_width="0dp"
                        app:flexWrap="wrap"
                        android:layout_height="wrap_content"
                        app:justifyContent="space_between"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/label_who_can_speak">

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioBtnSpeakAnyOne"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            app:layout_flexBasisPercent="45%"
                            android:checked="@{viewModel.whoCanSpeak==PodiumWhoCanJoin.ANY_ONE_CAN}"
                            android:onClick="@{() -> viewModel.setWhoCanSpeak(PodiumWhoCanJoin.ANY_ONE_CAN)}"
                            android:text="@string/podium_join_with_rating_anyone"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioSpeakAboveOrEqualNinety"
                            android:layout_width="0dp"
                            app:layout_flexBasisPercent="45%"
                            android:layout_height="wrap_content"
                            android:checked="@{viewModel.whoCanSpeak==PodiumWhoCanJoin.RATING_ABOVE_NINETY}"
                            android:onClick="@{() -> viewModel.setWhoCanSpeak(PodiumWhoCanJoin.RATING_ABOVE_NINETY)}"
                            android:text="@string/podium_join_rating_above_ninety"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioSpeakBtnOnlyHundred"
                            android:layout_width="0dp"
                            app:layout_flexBasisPercent="100%"
                            android:layout_height="wrap_content"
                            android:checked="@{viewModel.whoCanSpeak==PodiumWhoCanJoin.RATING_ONLY_HUNDRED}"
                            android:onClick="@{() -> viewModel.setWhoCanSpeak(PodiumWhoCanJoin.RATING_ONLY_HUNDRED)}"
                            android:text="@string/podium_join_only_hundred"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />
                    </com.google.android.flexbox.FlexboxLayout>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_speaking_fee"
                        style="@style/TextAppearance.Flashat.Label"
                        goneIfNot="@{viewModel.speakingFeeVisible}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:text="@string/podium_speaking_fee"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/flex_layout_speak" />

                    <com.google.android.flexbox.FlexboxLayout
                        android:id="@+id/flex_layout_speaking_fee"
                        goneIfNot="@{viewModel.speakingFeeVisible}"
                        android:layout_width="0dp"
                        app:flexWrap="wrap"
                        android:layout_height="wrap_content"
                        app:justifyContent="space_between"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/label_speaking_fee">

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioBtnSpeakFree"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            app:layout_flexBasisPercent="45%"
                            android:checked="@{viewModel.speakingFee == SpeakingJoiningFee.FREE}"
                            android:onClick="@{() -> viewModel.setSpeakingFee(SpeakingJoiningFee.FREE)}"
                            android:text="@string/podium_challenge_free"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioSpeakWithCoins"
                            android:layout_width="0dp"
                            app:layout_flexBasisPercent="45%"
                            android:layout_height="wrap_content"
                            android:checked="@{viewModel.speakingFee == SpeakingJoiningFee.CUSTOM}"
                            android:onClick="@{() -> viewModel.setSpeakingFee(SpeakingJoiningFee.CUSTOM)}"
                            android:text="@string/podium_theater_fee_hint"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/textInputSpeakFee"
                            style="@style/Widget.Flashat.GreyTextInput"
                            goneIfNot="@{viewModel.speakingFee == SpeakingJoiningFee.CUSTOM}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:importantForAutofill="yes"
                            app:hintAnimationEnabled="false"
                            app:hintEnabled="false"
                            app:layout_constraintTop_toBottomOf="@id/radioGroupStageFee">

                            <com.google.android.material.textfield.TextInputEditText
                                style="@style/Widget.Flashat.GreyTextInput"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="@string/podium_enter_fee_hint"
                                android:inputType="number"
                                android:maxLength="10"
                                android:text="@={viewModel.speakingFeeString}" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvSpeakFeeErrorText"
                                style="@style/TextAppearance.Flashat.Label"
                                android:layout_width="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_height="wrap_content"
                                tools:text="Please enter amount greater than 10"
                                android:text="@{@string/podium_fee_error_text(SpeakingJoiningFee.MINIMUM_CUSTOM_FEE)}"
                                android:textColor="@color/red"
                                app:invisibleIf="@{viewModel.isSpeakingFeeValid || viewModel.isSpeakingFeeEmpty}" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </com.google.android.flexbox.FlexboxLayout>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_fees_for_audience"
                        style="@style/TextAppearance.Flashat.Label"
                        goneIfNot="@{viewModel.kind == PodiumKind.THEATER}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:text="@string/podium_theater_fees_for_audience"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/flex_layout_speaking_fee"
                        tools:visibility="visible"/>

                    <com.google.android.flexbox.FlexboxLayout
                        android:id="@+id/radioAudienceFee"
                        goneIfNot="@{viewModel.kind == PodiumKind.THEATER}"
                        android:layout_width="0dp"
                        app:flexWrap="wrap"
                        android:layout_height="wrap_content"
                        app:justifyContent="space_between"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/label_fees_for_audience"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:visibility="visible">

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioBtnFree"
                            android:layout_width="0dp"
                            app:layout_flexBasisPercent="45%"
                            android:layout_height="wrap_content"
                            android:checked="@{viewModel.audienceFeeType==TheaterAudienceFee.FREE}"
                            android:onClick="@{() -> viewModel.setAudienceFee(TheaterAudienceFee.FREE)}"
                            android:text="@string/podium_challenge_free"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

<!--                        <androidx.appcompat.widget.AppCompatRadioButton-->
<!--                            android:id="@+id/radioBtnFiveCoins"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_marginStart="@dimen/activity_margin"-->
<!--                            android:checked="@{viewModel.audienceFeeType==TheaterAudienceFee.COINS_FIVE}"-->
<!--                            android:onClick="@{() -> viewModel.setAudienceFee(TheaterAudienceFee.COINS_FIVE)}"-->
<!--                            android:paddingStart="@dimen/line_spacing"-->
<!--                            android:paddingEnd="@dimen/activity_margin"-->
<!--                            android:text="@{@string/common_coins(TheaterAudienceFee.COINS_FIVE.amount)}"-->
<!--                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />-->

<!--                        <androidx.appcompat.widget.AppCompatRadioButton-->
<!--                            android:id="@+id/radioBtnTenCoins"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_marginStart="@dimen/activity_margin"-->
<!--                            android:checked="@{viewModel.audienceFeeType==TheaterAudienceFee.COINS_TEN}"-->
<!--                            android:onClick="@{() -> viewModel.setAudienceFee(TheaterAudienceFee.COINS_TEN)}"-->
<!--                            android:paddingStart="@dimen/line_spacing"-->
<!--                            android:paddingEnd="@dimen/activity_margin"-->
<!--                            android:text="@{@string/common_coins(TheaterAudienceFee.COINS_TEN.amount)}"-->
<!--                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />-->

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioBtnCustomAudienceFee"
                            android:layout_width="0dp"
                            app:layout_flexBasisPercent="45%"
                            android:layout_height="wrap_content"
                            android:checked="@{viewModel.AudienceFeeType==TheaterAudienceFee.CUSTOM}"
                            android:onClick="@{() -> viewModel.setAudienceFee(TheaterAudienceFee.CUSTOM)}"
                            android:text="@string/podium_theater_fee_hint"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/textInputAudienceFee"
                            style="@style/Widget.Flashat.GreyTextInput"
                            goneIfNot="@{viewModel.audienceFeeType==TheaterAudienceFee.CUSTOM}"
                            android:visibility="gone"
                            tools:visibility="visible"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginVertical="@dimen/element_spacing"
                            android:importantForAutofill="yes"
                            app:hintAnimationEnabled="false"
                            app:hintEnabled="false"
                            app:layout_constraintTop_toBottomOf="@id/radioAudienceFee"
                            app:layout_constraintStart_toStartOf="@id/label_fees_for_audience"
                            app:layout_constraintEnd_toEndOf="@id/label_fees_for_audience">

                            <com.google.android.material.textfield.TextInputEditText
                                style="@style/Widget.Flashat.GreyTextInput"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="@string/podium_enter_fee_hint"
                                android:inputType="number"
                                android:maxLength="10"
                                android:text="@={viewModel.audienceFeeString}" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvAudienceFeeErrorText"
                                style="@style/TextAppearance.Flashat.Label"
                                app:invisibleIf="@{viewModel.isAudienceFeeValid || viewModel.isAudienceFeeEmpty}"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{@string/podium_fee_error_text(TheaterAudienceFee.MINIMUM_CUSTOM_FEE)}"
                                tools:text="@string/podium_fee_error_text"
                                android:textColor="@color/red" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </com.google.android.flexbox.FlexboxLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layout_stage_fee"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="parent"
                        goneIfNot="@{viewModel.kind == PodiumKind.THEATER}"
                        app:layout_constraintTop_toBottomOf="@id/radioAudienceFee">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/label_stage_fee"
                            style="@style/TextAppearance.Flashat.Label"
                            goneIfNot="@{viewModel.kind == PodiumKind.THEATER}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            android:layout_marginTop="@dimen/activity_margin"
                            android:text="@string/podium_theater_stage_fee"
                            android:textColor="@color/textColorSecondary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:visibility="visible"/>

                        <com.google.android.flexbox.FlexboxLayout
                            android:id="@+id/radioGroupStageFee"
                            goneIfNot="@{viewModel.kind == PodiumKind.THEATER}"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            app:flexWrap="wrap"
                            app:justifyContent="space_between"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/label_stage_fee"
                            tools:visibility="visible">

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnHundredCoins"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.stageFeeType==TheaterStageFee.COINS_ZERO}"
                                android:onClick="@{() -> viewModel.setStageFee(TheaterStageFee.COINS_ZERO)}"
                                android:text="@string/podium_challenge_free"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2"
                                app:layout_flexBasisPercent="45%" />

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnCustomStageFee"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.stageFeeType==TheaterStageFee.CUSTOM}"
                                android:onClick="@{() -> viewModel.setStageFee(TheaterStageFee.CUSTOM)}"
                                android:text="@string/podium_theater_fee_hint"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2"
                                app:layout_flexBasisPercent="45%" />

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/textInputStageFee"
                                style="@style/Widget.Flashat.GreyTextInput"
                                goneIfNot="@{viewModel.stageFeeType==TheaterStageFee.CUSTOM}"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:importantForAutofill="yes"
                                android:visibility="visible"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false"
                                app:layout_constraintEnd_toEndOf="@id/label_stage_fee"
                                app:layout_constraintStart_toStartOf="@id/label_stage_fee"
                                app:layout_constraintTop_toBottomOf="@id/radioGroupStageFee">

                                <com.google.android.material.textfield.TextInputEditText
                                    style="@style/Widget.Flashat.GreyTextInput"
                                    goneIfNot="@{radioBtnCustomStageFee.checked}"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:hint="@string/podium_enter_fee_hint"
                                    android:inputType="number"
                                    android:maxLength="10"
                                    android:text="@={viewModel.stageFeeString}" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/tvStageFeeErrorText"
                                    style="@style/TextAppearance.Flashat.Label"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{@string/podium_fee_error_text(TheaterStageFee.MINIMUM_CUSTOM_FEE)}"
                                    android:textColor="@color/red"
                                    android:visibility="gone"
                                    app:invisibleIf="@{viewModel.isStageFeeValid || viewModel.isStageFeeEmpty}"
                                    tools:text="Enter amount greater than 10"
                                    tools:visibility="visible" />
                            </com.google.android.material.textfield.TextInputLayout>

                        </com.google.android.flexbox.FlexboxLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_joining_fee"
                        style="@style/TextAppearance.Flashat.Label"
                        goneIfNot="@{viewModel.joiningFeeVisible}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:text="@string/podium_joining_fee"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/layout_stage_fee" />

                    <com.google.android.flexbox.FlexboxLayout
                        android:id="@+id/flex_layout_joining_fee"
                        goneIfNot="@{viewModel.joiningFeeVisible}"
                        android:layout_width="0dp"
                        app:flexWrap="wrap"
                        android:layout_height="wrap_content"
                        app:justifyContent="space_between"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/label_joining_fee">

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioBtnJoinFree"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            app:layout_flexBasisPercent="45%"
                            android:checked="@{viewModel.joiningFee == SpeakingJoiningFee.FREE}"
                            android:onClick="@{() -> viewModel.setJoiningFee(SpeakingJoiningFee.FREE)}"
                            android:text="@string/podium_challenge_free"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                        <androidx.appcompat.widget.AppCompatRadioButton
                            android:id="@+id/radioJoinWithCoins"
                            android:layout_width="0dp"
                            app:layout_flexBasisPercent="45%"
                            android:layout_height="wrap_content"
                            android:checked="@{viewModel.joiningFee == SpeakingJoiningFee.CUSTOM}"
                            android:onClick="@{() -> viewModel.setJoiningFee(SpeakingJoiningFee.CUSTOM)}"
                            android:text="@string/podium_theater_fee_hint"
                            android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/textInputJoinFee"
                            style="@style/Widget.Flashat.GreyTextInput"
                            goneIfNot="@{viewModel.joiningFee == SpeakingJoiningFee.CUSTOM}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:importantForAutofill="yes"
                            app:hintAnimationEnabled="false"
                            app:hintEnabled="false"
                            app:layout_constraintTop_toBottomOf="@id/radioGroupStageFee">

                            <com.google.android.material.textfield.TextInputEditText
                                style="@style/Widget.Flashat.GreyTextInput"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="@string/podium_enter_fee_hint"
                                android:inputType="number"
                                android:maxLength="10"
                                android:text="@={viewModel.joiningFeeString}" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvJoinFeeErrorText"
                                style="@style/TextAppearance.Flashat.Label"
                                android:layout_width="wrap_content"
                                tools:text="Please enter amount greater than 10"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_height="wrap_content"
                                android:text="@{@string/podium_fee_error_text(SpeakingJoiningFee.MINIMUM_CUSTOM_FEE)}"
                                android:textColor="@color/red"
                                app:invisibleIf="@{viewModel.isJoiningFeeValid || viewModel.isJoiningFeeEmpty}" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </com.google.android.flexbox.FlexboxLayout>


                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_about"
                        style="@style/TextAppearance.Flashat.Label"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:text="@string/create_podium_label_welcome_message"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/flex_layout_joining_fee"
                        tools:text="Welcome Message”" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/text_input_above"
                        style="@style/Widget.Flashat.GreyTextInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:autofillHints="name"
                        android:enabled="@{!viewModel.createPodiumLoading}"
                        android:importantForAutofill="yes"
                        app:counterEnabled="true"
                        app:counterMaxLength="@{viewModel.aboutMaxLength}"
                        app:endIconMode="custom"
                        app:endIconTint="@color/textColorSecondaryLight"
                        app:hintAnimationEnabled="false"
                        app:hintEnabled="false"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/label_about">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/text_input_podium_about"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="top"
                            android:hint="@string/create_podium_enter_welcome_messages"
                            android:inputType="textMultiLine"
                            android:lines="5"
                            android:maxLength="@{viewModel.aboutMaxLength}"
                            android:text="@={viewModel.about}" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/action_go_live_button"
                        style="@style/Widget.Flashat.LargeRoundedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/double_margin"
                        android:layout_marginTop="@dimen/extra_margin"
                        android:checkable="true"
                        android:enabled="@{viewModel.createButtonEnable}"
                        android:text="@{viewModel.editMode==false?@string/podium_go_live:@string/update_podium_button_go_live}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/text_input_above"
                        tools:text="@string/podium_go_live" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/action_save_as_draft"
                        style="@style/Widget.Flashat.pollButton.outline"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/line_spacing"
                        android:enabled="@{viewModel.createButtonEnable}"
                        android:text="@string/podium_save_draft"
                        android:textStyle="normal"
                        app:layout_constraintEnd_toEndOf="@id/action_go_live_button"
                        app:layout_constraintStart_toStartOf="@id/action_go_live_button"
                        app:layout_constraintTop_toBottomOf="@+id/action_go_live_button"
                        tools:text="@string/podium_save_draft" />

                    <com.google.android.material.progressindicator.LinearProgressIndicator
                        android:id="@+id/login_progress"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="6dp"
                        android:indeterminate="true"
                        app:goneIfNot="@{viewModel.createPodiumLoading}"
                        app:hideAnimationBehavior="inward"
                        app:layout_constraintBottom_toBottomOf="@id/action_save_as_draft"
                        app:layout_constraintEnd_toEndOf="@id/action_save_as_draft"
                        app:layout_constraintStart_toStartOf="@id/action_save_as_draft"
                        app:showAnimationBehavior="outward"
                        app:trackCornerRadius="@dimen/line_spacing" />


                    <Button
                        android:id="@+id/action_podium_delete"
                        style="@style/Widget.Flashat.LargeRoundedButton.Outline.Negative"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/activity_margin"
                        android:text="@string/action_delete_podium"
                        app:goneIfNot="@{viewModel.canDeletePodium}"
                        app:layout_constraintEnd_toEndOf="@id/action_go_live_button"
                        app:layout_constraintStart_toStartOf="@id/action_go_live_button"
                        app:layout_constraintTop_toBottomOf="@+id/action_save_as_draft" />


                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/label_podium_policy"
                        style="@style/TextAppearance.Flashat.Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/public_create_huddle_read_more_about"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintBaseline_toBaselineOf="@id/podium_policy_button"
                        app:layout_constraintEnd_toStartOf="@+id/podium_policy_button"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        tools:layout_editor_absoluteY="571dp" />

                    <Button
                        android:id="@+id/podium_policy_button"
                        style="@style/Widget.Flashat.TextButton.Link"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:enabled="@{!viewModel.createPodiumLoading}"
                        android:text="@string/document_podium_policy"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toEndOf="@+id/label_podium_policy"
                        app:layout_constraintTop_toBottomOf="@id/action_podium_delete"
                        tools:text="Podium Policy" />
                </androidx.constraintlayout.widget.ConstraintLayout>


            </ScrollView>

        </com.kennyc.view.MultiStateView>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>


</layout>
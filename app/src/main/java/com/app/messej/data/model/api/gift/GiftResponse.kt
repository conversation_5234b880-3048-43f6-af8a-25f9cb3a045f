package com.app.messej.data.model.api.gift


import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.google.gson.annotations.SerializedName

data class GiftResponse(
    @SerializedName("flax") val flax: Double,
    @SerializedName("packages_list") val packagesList: List<PointsPurchase>,
    @SerializedName("received_coins") val receivedCoins: Double,
    @SerializedName("user_gift_data") val userGiftData: GiftData,
    @SerializedName("generosity_status_section") val generosityStatusSection: GenerosityStatusSection,
    @SerializedName("total_section") val totalSection: TotalSection,
    @SerializedName("monthly_section") val monthlySection: MonthlySection

){
    val roundReceivedCoins: Long
        get() = Math.round(receivedCoins)

}

data class GiftData(
    @SerializedName("total_received_gifts") val totalReceivedGifts: Double,
    @SerializedName("total_available_gift_values") val totalAvailableGiftValues: Double,
    @SerializedName("total_converted_coins") val totalConvertedPoints: Double,
    @SerializedName("total_coins_balance") val totalPointsBalance: Double,
    @SerializedName("minimum_coin_conversion") val minimumCoinConversion: Double,
    @SerializedName("minimum_flax_conversion") val minimumFlaxConversion: Double,
    @SerializedName("coin_flax_conversion_value") val coinFlaxConversionValue: Double,
    @SerializedName("total_flix_received_gifts") val totalFlixReceivedGifts: Double,
    )

data class GenerosityStatusSection(
    @SerializedName("total_coins_gained") val totalCoinsGained: Double,
    @SerializedName("total_coins_given") val totalCoinsGiven: Double,
    @SerializedName("give_away_percentage") val giveAwayPercentage: Double,
    @SerializedName("total_coin_obligation") val totalCoinObligation: Double,
){
    val coinsGivenFormatted: String
        get() = totalCoinsGiven.toInt().numberToKWithFractions()

    val coinsGainedFormatted: String
        get() = totalCoinsGained.toInt().numberToKWithFractions()

    val totalCoinObligationFormatted: String
        get() = totalCoinObligation.toInt().numberToKWithFractions()
}

data class TotalSection(
    @SerializedName("total_received_gifts") val totalReceivedGifts: Double,
    @SerializedName("total_coins_received_gifts") val totalCoinsReceivedGifts: Double,
    @SerializedName("total_flix_received_gifts") val totalFlixReceivedGifts: Double,
    @SerializedName("total_converted_coins") val totalConvertedCoins: Double,
    @SerializedName("total_sent_gifts") val totalSentGifts: Double,
    @SerializedName("total_coins_sent_gifts") val totalCoinsSentGifts: Double,
    @SerializedName("total_give_away_percentage") val totalGiveAwayPercentage: Double,
){
    val totalProgress: Double
        get() = totalGiveAwayPercentage / 100.00
}

data class MonthlySection(
    @SerializedName("monthly_received_gifts") val monthlyReceivedGifts: Double,
    @SerializedName("monthly_coins_received_gifts") val monthlyCoinsReceivedGifts: Double,
    @SerializedName("monthly_flix_received_gifts") val monthlyFlixReceivedGifts: Double,
    @SerializedName("monthly_converted_coins") val monthlyConvertedCoins: Double,
    @SerializedName("monthly_sent_gifts") val monthlySentGifts: Double,
    @SerializedName("monthly_coins_sent_gifts") val monthlyCoinsSentGifts: Double,
    @SerializedName("monthly_give_away_percentage") val monthlyGiveAwayPercentage: Double,
){
    val monthlyProgress: Double
        get() = monthlyGiveAwayPercentage / 100.00
}


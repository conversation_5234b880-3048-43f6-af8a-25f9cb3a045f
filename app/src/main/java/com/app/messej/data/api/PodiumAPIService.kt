package com.app.messej.data.api

import com.app.messej.data.model.BirthdayPodiumTopGiftersResponse
import com.app.messej.data.model.PaidLikePayload
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.podium.FlagChallengeAnswerRequest
import com.app.messej.data.model.api.podium.HideLiveUsersRequest
import com.app.messej.data.model.api.podium.PodiumAbout
import com.app.messej.data.model.api.podium.PodiumBuyCameraResponse
import com.app.messej.data.model.api.podium.PodiumCategoriesResponse
import com.app.messej.data.model.api.podium.PodiumChallengeQuestionResponse
import com.app.messej.data.model.api.podium.PodiumInvitationRequest
import com.app.messej.data.model.api.podium.PodiumJoinResponse
import com.app.messej.data.model.api.podium.PodiumLiveFriendsResponse
import com.app.messej.data.model.api.podium.PodiumLiveUsersListResponse
import com.app.messej.data.model.api.podium.PodiumMaidanUsersListResponse
import com.app.messej.data.model.api.podium.PodiumRecordsResponse
import com.app.messej.data.model.api.podium.PodiumResponse
import com.app.messej.data.model.api.podium.PodiumSettingsRequest
import com.app.messej.data.model.api.podium.PodiumUserLikesCoinsResponse
import com.app.messej.data.model.api.podium.PodiumUsersListResponse
import com.app.messej.data.model.api.podium.YallaGuysListResponse
import com.app.messej.data.model.api.podium.challenges.CreateMaidanRequest
import com.app.messej.data.model.api.podium.challenges.EndMaidanChallengeRequest
import com.app.messej.data.model.api.podium.challenges.MaidanChallengeAgainRequest
import com.app.messej.data.model.api.podium.challenges.MaidanEditResponse
import com.app.messej.data.model.api.podium.challenges.MaidanLikeRequest
import com.app.messej.data.model.api.podium.challenges.MaidanLikeResponse
import com.app.messej.data.model.api.podium.challenges.MaidanPlayerSummary
import com.app.messej.data.model.api.podium.challenges.MaidanStatsResponse
import com.app.messej.data.model.api.podium.challenges.MaidanSupporterMode
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeContributorRequest
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeTimerRequest
import com.app.messej.data.model.api.podium.challenges.PodiumConFourChallengeInviteParticipantRequest
import com.app.messej.data.model.api.podium.challenges.PodiumConFourInviteResponse
import com.app.messej.data.model.api.podium.challenges.PodiumContributorResponse
import com.app.messej.data.model.api.podium.challenges.PodiumCreateChallengeRequest
import com.app.messej.data.model.api.podium.challenges.PodiumGiftSupportersResponse
import com.app.messej.data.model.api.podium.challenges.YallaGuysJoinResponse
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.AcceptDecline
import com.app.messej.data.model.enums.ConfirmDecline
import com.app.messej.data.model.enums.TheaterJoinType
import com.app.messej.data.model.enums.UserCitizenship
import okhttp3.MultipartBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query

interface PodiumAPIService {

    @Multipart
    @POST("/podium")
    @Headers("Accept: application/json")
    suspend fun createPodium(@Part file: MultipartBody.Part?, @Part data: MultipartBody.Part): Response<APIResponse<Podium>>

    @Multipart
    @PUT("/podium/{id}")
    @Headers("Accept: application/json")
    suspend fun updatePodium(
        @Path("id") podiumId: String,
        @Part file: MultipartBody.Part?,
        @Part data: MultipartBody.Part,
    ): Response<APIResponse<Podium>>

    @GET("/podium/category")
    @Headers("Accept: application/json")
    suspend fun getPodiumCategories(): Response<APIResponse<PodiumCategoriesResponse>>

    @GET("/podium")
    @Headers("Accept: application/json")
    suspend fun getPodiumList(
        @Query("tab") tab: String,
        @Query("page") page: Int,
        @Query("per_page") perPage: Int = 25,
        @Query("kind") kind: String? = null
    ): Response<APIResponse<PodiumResponse>>

    @GET("/podium")
    @Headers("Accept: application/json")
    suspend fun getPodiumSearchList(@Query("tab") tab: String, @Query("keyword") keyword: String, @Query("page") page: Int): Response<APIResponse<PodiumResponse>> //TODO Add correct API

    @GET("/podium/{id}")
    @Headers("Accept: application/json")
    suspend fun getPodiumDetails(
        @Path("id") id: String,
        @Query("invited_participants") invitedParticipants: String? = null,
    ): Response<APIResponse<Podium>>

    @POST("/podium/{id}/invite")
    @Headers("Accept: application/json")
    suspend fun sendPodiumInvitation(@Path("id") id: String, @Body request: PodiumInvitationRequest): Response<APIResponse<Unit>>

    @POST("/podium/{id}/go-live")
    @Headers("Accept: application/json")
    suspend fun goLive(@Path("id") id: String): Response<APIResponse<PodiumJoinResponse>>

    @POST("/podium/{id}/join")
    @Headers("Accept: application/json")
    suspend fun join(@Path("id") id: String/*, @Query("podium_join_hidden") joinHidden: Int = 0*/): Response<APIResponse<PodiumJoinResponse>>

    @GET("/podium/{id}/waiting-participant")
    @Headers("Accept: application/json")
    suspend fun getWaitingList(@Path("id") id: String): Response<APIResponse<PodiumUsersListResponse>>

    @GET("/podium/{id}/waiting-participant")
    @Headers("Accept: application/json")
    suspend fun getWaitingList(
        @Path("id") id: String,
        @Query("page") page: Int,
        @Query("per_page") perPage: Int = 50,
        @Query("search") searchTerm: String? = null,
    ): Response<APIResponse<PodiumUsersListResponse>>

    @POST("/podium/{id}/request-to-speak")
    @Headers("Accept: application/json")
    suspend fun requestToSpeak(@Path("id") id: String): Response<APIResponse<Unit>>

    @POST("/podium/{id}/request-to-speak")
    @Headers("Accept: application/json")
    suspend fun requestToSpeakInTheater(@Path("id") id: String, @Query("action") action: String): Response<APIResponse<Unit>>

    @DELETE("/podium/{id}/request-to-speak")
    @Headers("Accept: application/json")
    suspend fun cancelRequestToSpeak(@Path("id") id: String): Response<APIResponse<Unit>>

    @POST("/podium/{id}/allow-to-speak/{userId}")
    @Headers("Accept: application/json")
    suspend fun allowUserToSpeak(@Path("id") podiumId: String, @Path("userId") userId: Int): Response<APIResponse<Unit>>

    @POST("/podium/{id}/leave")
    @Headers("Accept: application/json")
    suspend fun leavePodium(@Path("id") podiumId: String): Response<APIResponse<Unit>>

    @POST("/podium/{id}/exit")
    @Headers("Accept: application/json")
    suspend fun exitPodium(@Path("id") podiumId: String): Response<APIResponse<Unit>>

    @POST("/podium/{id}/close")
    @Headers("Accept: application/json")
    suspend fun closePodium(@Path("id") podiumId: String): Response<APIResponse<String>>

    @PUT("/podium/{id}/block/{userId}")
    @Headers("Accept: application/json")
    suspend fun blockPodiumParticipant(
        @Path("id") podiumId: String,
        @Path("userId") userId: Int,
        @Query("action") action: String,
        @Query("block_from") blockFrom: String? = null
    ): Response<APIResponse<Unit>>

    @GET("/podium/{id}/block")
    @Headers("Accept: application/json")
    suspend fun getPodiumBlockedUserList(@Path("id") id: String, @Query("page") page: Int = 1, @Query("per_page") perPage: Int = 50): Response<APIResponse<PodiumUsersListResponse>>

    @GET("/podium/{id}/freeze")
    @Headers("Accept: application/json")
    suspend fun getPodiumFrozenUserList(@Path("id") id: String, @Query("page") page: Int = 1, @Query("per_page") perPage: Int = 50): Response<APIResponse<PodiumUsersListResponse>>

    @POST("/podium/{id}/mute-participant/{participant_id}")
    @Headers("Accept: application/json")
    suspend fun muteParticipant(@Path("id") podiumId: String, @Path("participant_id") participantId: Int, @Query("action") action: String): Response<APIResponse<Unit>>

    @PUT("/podium/{id}/mute")
    @Headers("Accept: application/json")
    suspend fun muteSelf(@Path("id") podiumId: String, @Query("action") action: String): Response<APIResponse<Unit>>

    @PUT("/podium/{id}/freeze/{participant_id}?action=freeze")
    @Headers("Accept: application/json")
    suspend fun freezeParticipant(@Path("id") podiumId: String, @Path("participant_id") participantId: Int): Response<APIResponse<Unit>>

    @PUT("/podium/{id}/freeze/{participant_id}?action=unfreeze")
    @Headers("Accept: application/json")
    suspend fun unfreezeParticipant(@Path("id") podiumId: String, @Path("participant_id") participantId: Int): Response<APIResponse<Unit>>

    @POST("/podium/{id}/end-speaking-session/{participant_id}")
    @Headers("Accept: application/json")
    suspend fun endParticipantSpeakingSession(@Path("id") podiumId: String, @Path("participant_id") participantId: Int): Response<APIResponse<Unit>>

    @POST("/podium/{id}/end-speaking")
    @Headers("Accept: application/json")
    suspend fun endSelfSpeakingSession(@Path("id") podiumId: String): Response<APIResponse<Unit>>

    @POST("/podium/{id}/admins/{participant-id}/request")
    @Headers("Accept: application/json")
    suspend fun appointAnAdmin(@Path("id") podiumId: String, @Path("participant-id") participantId: Int): Response<APIResponse<Unit>>

    @POST("/podium/{id}/admins/{participant-id}/dismiss")
    @Headers("Accept: application/json")
    suspend fun dismissAnAdmin(@Path("id") podiumId: String, @Path("participant-id") participantId: Int): Response<APIResponse<Unit>>

    @POST("/podium/{id}/admins/{participant-id}/cancel")
    @Headers("Accept: application/json")
    suspend fun cancelAdminAppoint(@Path("id") podiumId: String, @Path("participant-id") participantId: Int): Response<APIResponse<Unit>>

    @PUT("/podium/{id}/admins/{participant-id}/confirm")
    @Headers("Accept: application/json")
    suspend fun confirmAdminAppointRequest(@Path("id") podiumId: String, @Path("participant-id") participantId: Int, @Query("action") action: String): Response<APIResponse<Unit>>

    @GET("/podium/{id}/admins")
    @Headers("Accept: application/json")
    suspend fun getAdminsList(@Path("id") podiumId: String, @Query("page") page: Int = 1, @Query("per_page") perPage: Int = 50): Response<APIResponse<PodiumUsersListResponse>>

    @POST("/podium/{id}/decline-invitation")
    @Headers("Accept: application/json")
    suspend fun declinePodiumInvitation(@Path("id") podiumId: String): Response<APIResponse<Unit>>

    @POST("/podium/{id}/request-to-speak/decline/{participant-id}")
    @Headers("Accept: application/json")
    suspend fun declineRequestToSpeak(@Path("id") podiumId: String, @Path("participant-id") participantId: Int): Response<APIResponse<Unit>>

    @GET("/podium/{id}/users/live")
    @Headers("Accept: application/json")
    suspend fun getLiveUsersList(
        @Path("id") podiumId: String,
        @Query("page") page: Int,
        @Query("search") searchTerm: String? = null,
        @Query("exclude") exclude : UserCitizenship?,
        @Query("exclude_reported") excludeReportedUser : Boolean? = null,
    ): Response<APIResponse<PodiumLiveUsersListResponse>>

    @GET("/podium/{id}/stats/about")
    @Headers("Accept: application/json")
    suspend fun getPodiumAbout(@Path("id") podiumId: String): Response<APIResponse<PodiumAbout>>

    @GET("/podium/{id}/stats/records")
    @Headers("Accept: application/json")
    suspend fun getPodiumRecords(@Path("id") podiumId: String, @Query("page") page: Int): Response<APIResponse<PodiumRecordsResponse>>

    @GET("/podium/{id}/users/{userId}")
    @Headers("Accept: application/json")
    suspend fun getPodiumUserLikesCoins(@Path("id") podiumId: String, @Path("userId") participantId: Int): Response<APIResponse<PodiumUserLikesCoinsResponse>>

    @PUT("/podium/{id}/settings")
    @Headers("Accept: application/json")
    suspend fun podiumSettings(@Path("id") podiumId: String, @Body podiumSettingsRequest: PodiumSettingsRequest): Response<APIResponse<Unit>>

    @POST("/podium/{podium_id}/pause-gift")
    @Headers("Accept: application/json")
    suspend fun podiumPauseGift(@Path("podium_id") podiumId: String, @Query("action") action:String): Response<APIResponse<Unit>>

    @POST("/podium/{podium_id}/pause-gift/{participant_id}")
    @Headers("Accept: application/json")
    suspend fun podiumPauseUserGift(@Path("podium_id") podiumId: String,@Path("participant_id")participantId: Int, @Query("action") action:String): Response<APIResponse<Unit>>

    @DELETE("/podium/{id}")
    @Headers("Accept: application/json")
    suspend fun deletePodium(@Path("id") podiumId: String): Response<APIResponse<Unit>>

    @POST("/podium/{id}/challenges")
    @Headers("Accept: application/json")
    suspend fun createChallenges(@Path("id") podiumId: String, @Body request: PodiumCreateChallengeRequest): Response<APIResponse<PodiumChallenge>>

    @POST("/podium/{id}/challenges/{challenge_id}/facilitator/{user_id}")
    @Headers("Accept: application/json")
    suspend fun appointFacilitator(
        @Path("id") podiumId: String,
        @Path("challenge_id") challengeId: String,
        @Path("user_id") facilitatorId: Int
    ): Response<APIResponse<PodiumChallenge>>

    @POST("/podium/{id}/challenges/{challenge_id}/facilitator/{user_id}/confirm")
    @Headers("Accept: application/json")
    suspend fun acceptDeclineFacilitator(
        @Path("id") podiumId: String,
        @Path("challenge_id") challengeId: String,
        @Path("user_id") facilitatorId: Int,
        @Query("action") action : ConfirmDecline = ConfirmDecline.CONFIRM
    ): Response<APIResponse<Unit>>


    @POST("/podium/{id}/challenges/{challenge_id}/timer")
    @Headers("Accept: application/json")
    suspend fun setChallengeTimer(
        @Path("id") podiumId: String,
        @Path("challenge_id") challengeId: String,
        @Body request: PodiumChallengeTimerRequest
    ): Response<APIResponse<PodiumChallenge>>

    /**
     * New appoint contributor api for SPEAKERS and CONTRIBUTOR
     */
    @POST("/podium/{id}/challenges/{challenge_id}/choose-contributors")
    @Headers("Accept: application/json")
    suspend fun appointContributor(
        @Path("id") podiumId: String,
        @Path("challenge_id") challengeId: String,
        @Body request: PodiumChallengeContributorRequest
    ): Response<APIResponse<PodiumContributorResponse>>

    @PUT("/podium/{id}/challenges/{challenge_id}/contributors/{user_id}/confirm")
    @Headers("Accept: application/json")
    suspend fun acceptDeclineContributor(
        @Path("id") podiumId: String,
        @Path("challenge_id") challengeId: String,
        @Path("user_id") contributorId: Int,
        @Query("action") action : ConfirmDecline = ConfirmDecline.CONFIRM
    ): Response<APIResponse<Unit>>

    @POST("/podium/{id}/challenges/{c_id}/start")
    @Headers("Accept: application/json")
    suspend fun startPodiumChallenge(
        @Path("id") podiumId: String?,
        @Path("c_id") challengeId: String?
    ): Response<APIResponse<Unit>>

    @PUT("/podium/{id}/challenges/{c_id}/close")
    @Headers("Accept: application/json")
    suspend fun closePodiumChallenge(
        @Path("id") podiumId: String?,
        @Path("c_id") challengeId: String?
    ): Response<APIResponse<Unit>>

    @DELETE("podium/{id}/challenges/{c_id}")
    @Headers("Accept: application/json")
    suspend fun deletePodiumChallenge(
        @Path("id") podiumId: String?,
        @Path("c_id") challengeId: String?
    ): Response<APIResponse<Unit>>

    @GET("/podium/{id}/challenges/{ch_id}/supporters")
    @Headers("Accept: application/json")
    suspend fun getGiftChallengeSupporters(@Path("id") podiumId: String, @Path("ch_id") challengesId: String?, @Query("page") page: Int, @Query("per_page") perPage: Int,): Response<APIResponse<PodiumGiftSupportersResponse>>

    @GET("/podium/{podium_id}/challenges/{challenge_id}")
    @Headers("Accept: application/json")
    suspend fun getChallengeDetails(@Path("podium_id") podiumId: String, @Path("challenge_id") challengeId: String): Response<APIResponse<PodiumChallenge>>

    @POST("/podium/{id}/speaker/invite/{user_id}")
    @Headers("Accept: application/json")
    suspend fun inviteToSpeak(@Path("id") podiumId: String, @Path("user_id") userId: Int, @Query("action") action: TheaterJoinType?, @Query("invited_for_free") invitedForFree: Boolean = true): Response<APIResponse<Unit>>

    @POST("/podium/{id}/speaker/invite/{user_id}/cancel")
    @Headers("Accept: application/json")
    suspend fun cancelInviteToSpeak(@Path("id") podiumId: String, @Path("user_id") userId: Int): Response<APIResponse<Unit>>

    @POST("/podium/{id}/challenges/invite/reply")
    @Headers("Accept: application/json")
    suspend fun replyChallengeSpeakerInvite(@Path("id") podiumId: String, @Query("action") action: AcceptDecline): Response<APIResponse<Unit>>

    @POST("/podium/{id}/speaker/invite/reply")
    @Headers("Accept: application/json")
    suspend fun replySpeakerInvite(@Path("id") podiumId: String, @Query("action") action: AcceptDecline, @Query("join_action") joinType: TheaterJoinType?, @Query("invited_for_free") invitedForFree: Boolean?): Response<APIResponse<Unit>>

    @GET("/podium/{podium_id}/challenges/{challenge_id}/questions")
    @Headers("Accept: application/json")
    suspend fun getFlagChallengeQuestions(
        @Path("podium_id") podiumId: String,@Path("challenge_id") challengeId: String
    ): Response<APIResponse<PodiumChallengeQuestionResponse>>

    @POST("/podium/{podium_id}/challenges/{challenge_id}/questions")
    @Headers("Accept: application/json")
    suspend fun createQuestions(
        @Path("podium_id") podiumId: String,@Path("challenge_id") challengeId: String
    ): Response<APIResponse<PodiumChallengeQuestionResponse>>

    @POST("/podium/{podium_id}/challenges/{challenge_id}/answer")
    @Headers("Accept: application/json")
    suspend fun submitAnswer(@Path("podium_id") podiumId: String, @Path("challenge_id") challengeId: String, @Body verifyOtp: FlagChallengeAnswerRequest): Response<APIResponse<Unit>>

    @POST("/podium/{id}/challenges/{c_id}/invite")
    @Headers("Accept: application/json")
    suspend fun inviteChallengeParticipant(
        @Path("id") podiumId: String,
        @Path("c_id") challengeId: String,
        @Body request: PodiumConFourChallengeInviteParticipantRequest,
    ): Response<APIResponse<PodiumConFourInviteResponse>>

    @PUT(" /podium/{podium_id}/challenges/{challenge_id}/exit")
    @Headers("Accept: application/json")
    suspend fun exitChallenge(@Path("podium_id") podiumId: String, @Path("challenge_id") challengeId: String): Response<APIResponse<Unit>>

    @PUT("/podium/{podium_id}/extend-speaking-time/{user_id}")
    @Headers("Accept: application/json")
    suspend fun extendSpeakingTime(@Path("podium_id") podiumId: String, @Path("user_id") userId: Int): Response<APIResponse<Unit>>

    @GET("/user/cameratime")
    @Headers("Accept: application/json")
    suspend fun getPodiumBuyCameraDetails(): Response<APIResponse<PodiumBuyCameraResponse>>

    @POST("/user/cameratime")
    @Headers("Accept: application/json")
    suspend fun buyCameraTime(@Query("buy") buy:Boolean): Response<APIResponse<Unit>>

    @GET("podium/live-friends")
    @Headers("Accept: application/json")
    suspend fun getPodiumLiveFriends(@Query("page") page: Int, @Query("per_page") limit: Int): Response<APIResponse<PodiumLiveFriendsResponse>>

    @POST("/podium/{id}/hide-live-participants")
    @Headers("Accept: application/json")
    suspend fun podiumHideLiveUsers(@Path("id") podiumId: String, @Body hideLiveUsersRequest: HideLiveUsersRequest): Response<APIResponse<Unit>>

    @POST("/podium/maidans")
    @Headers("Accept: application/json")
    suspend fun createMaidanChallenge(@Body createMaidanRequest: CreateMaidanRequest): Response<APIResponse<Podium>>

    @PUT("/podium/maidans/{id}/edit")
    @Headers("Accept: application/json")
    suspend fun editMaidanChallenge(@Path("id") podiumId: String, @Body createMaidanRequest: CreateMaidanRequest): Response<APIResponse<MaidanEditResponse>>

    @GET("/podium/maidans")
    @Headers("Accept: application/json")
    suspend fun getPodiumMaidanList(
        @Query("tab") tab: String,
        @Query("sub_tab") subTab: String,
        @Query("page") page: Int,
        @Query("per_page") perPage: Int = 25
    ): Response<APIResponse<PodiumResponse>>

    @POST("/podium/maidans/{id}/join")
    @Headers("Accept: application/json")
    suspend fun joinMaidanChallenge(@Path("id") podiumId: String): Response<APIResponse<Podium>>

    @PUT("/podium/maidans/{id}/end")
    @Headers("Accept: application/json")
    suspend fun endMaidanChallenge(@Path("id") podiumId: String): Response<APIResponse<Unit>>

    @POST("/podium/maidans/{sub_podium_id}/end-challenge")
    @Headers("Accept: application/json")
    suspend fun endMaidanChallengeEmpowered(@Path("sub_podium_id") subPodiumId: String, @Body request: EndMaidanChallengeRequest): Response<APIResponse<Unit>>

    @POST("/podium/maidans/{pid}/challenges/{cid}/like?version=2")
    @Headers("Accept: application/json")
    suspend fun sendMaidanLike(@Path("pid") podiumId: String, @Path("cid") challengeId: String, @Body request: MaidanLikeRequest): Response<APIResponse<MaidanLikeResponse>>

    @PUT("/podium/maidans/challenges/{cid}/contributors/respond")
    @Headers("Accept: application/json")
    suspend fun respondToMaidanInvite(@Path("cid") challengeId: String, @Query("action") action : ConfirmDecline): Response<APIResponse<Podium?>>

    @GET("/podium/maidans/{pid}/challenges/{cid}/top-supporters")
    suspend fun getMaidanTopSupportersList(@Path("pid") podiumId: String, @Path("cid") challengeId: String, @Query("page") page: Int = 1): Response<APIResponse<PodiumMaidanUsersListResponse>>

    @GET("/podium/maidans/challenges/summary")
    suspend fun getMaidanStatsSummary(): Response<APIResponse<MaidanStatsResponse>>

    @GET("/podium/maidans/challenges/history")
    suspend fun getChallengeHistory(
        @Query("page") page: Int = 1,
        @Query("per_page") perPage: Int = 50,
    ): Response<APIResponse<PodiumMaidanUsersListResponse>>

    @GET("/podium/maidans/challenges/competitor-stats")
    suspend fun getCompetitorStats(
        @Query("page") page: Int = 1,
        @Query("per_page") perPage: Int = 50,
    ): Response<APIResponse<PodiumMaidanUsersListResponse>>

    @POST("/podium/maidans")
    @Headers("Accept: application/json")
    suspend fun challengeAgain(@Body req: MaidanChallengeAgainRequest): Response<APIResponse<PodiumChallenge>>

    @POST("/user/likes")
    @Headers("Accept: application/json")
    suspend fun sendPaidLike(@Body request: PaidLikePayload): Response<APIResponse<Unit>>

    @GET("/podium/{id}/charges")
    @Headers("Accept: application/json")
    suspend fun getTheaterCharges(
        @Path("id") podiumId: String,
        @Query("page") page: Int = 1,
        @Query("per_page") perPage: Int = 50,
        @Query("action") action: String,
    ): Response<APIResponse<PodiumMaidanUsersListResponse>>

    @GET("/podium/{id}/likes")
    @Headers("Accept: application/json")
    suspend fun getTheaterLikes(
        @Path("id") podiumId: String,
        @Query("page") page: Int = 1,
    ): Response<APIResponse<PodiumMaidanUsersListResponse>>

    @GET("/podium/maidans/user/{id}/stats")
    @Headers("Accept: application/json")
    suspend fun getMaidanPlayerSummary(
        @Path("id") userId: Int,
        @Query("competitor_user_id") competitorId: Int?,
    ): Response<APIResponse<MaidanPlayerSummary>>

    @GET("/podium/{id}/challenges/yalla?per_page=50")
    @Headers("Accept: application/json")
    suspend fun getYallaChallengesList(
        @Path("id") podiumId: String,
        @Query("page") page: Int = 1,
    ): Response<APIResponse<YallaGuysListResponse>>

    @POST("/podium/{id}/challenges/yalla/{challengeid}/join")
    @Headers("Accept: application/json")
    suspend fun joinYallaChallenge(
        @Path("id") podiumId: String,
        @Path("challengeid") challengeId: String,
    ): Response<APIResponse<YallaGuysJoinResponse>>

    @PUT("/podium/{id}/challenges/yalla/pause")
    @Headers("Accept: application/json")
    suspend fun pauseYallaGuys(
        @Path("id") podiumId: String,
    ): Response<APIResponse<Unit>>

    @PUT("/podium/{id}/challenges/yalla/resume")
    @Headers("Accept: application/json")
    suspend fun resumeYallaGuys(
        @Path("id") podiumId: String,
    ): Response<APIResponse<Unit>>

    @POST("/podium/{id}/start-anthem")
    @Headers("Accept: application/json")
    suspend fun playFlashatAnthem(
        @Path("id") podiumId: String,
    ): Response<APIResponse<Podium.Anthem>>

    @GET("/podium/{id}/fetch-gifters")
    @Headers("Accept: application/json")
    suspend fun getBirthdayPodiumTopGifters(
        @Path("id") podiumId: String,
        @Query("page") page: Int = 1,
        @Query("per_page") perPage: Int = 50,
    ): Response<APIResponse<BirthdayPodiumTopGiftersResponse>>

    @GET("/podium/{id}/fetch-gifters")
    @Headers("Accept: application/json")
    suspend fun getBirthdayPodiumGiftCheck(
        @Path("id") podiumId: String,
        @Query("user_sent_gift") userSentGift: Boolean=true
    ): Response<APIResponse<BirthdayPodiumTopGiftersResponse>>

    @PUT("/podium/maidans/{id}/supporter-mode")
    @Headers("Accept: application/json")
    suspend fun setMaidanSupporterMode(
        @Path("id") podiumId: String,
        @Query("supporter_mode") isSupporter: Boolean,
    ): Response<APIResponse<MaidanSupporterMode>>
}
package com.app.messej.ui.home.gift.composeScreen

import android.util.Log
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.FloatRange
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.app.messej.R
import com.app.messej.data.model.api.gift.GenerosityStatusSection
import com.app.messej.data.model.api.gift.MonthlySection
import com.app.messej.data.model.api.gift.TotalSection
import com.app.messej.ui.composeComponents.CustomLargeRoundButton
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.composeComponents.ShimmerDefaultCircleItem
import com.app.messej.ui.composeComponents.ShimmerDefaultItem
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomHorizontalSpacer
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros

@Composable
fun BalanceScreen(flixBalance: String? = null, coinBalance: String? = null) {
    Row(modifier = Modifier.padding(all = dimensionResource(id = R.dimen.element_spacing))) {
        flixBalance?.let { BalanceItemSingleItem(header = stringResource(R.string.total_flax), value = it) }
        CustomHorizontalSpacer(R.dimen.extra_margin)
        VerticalDivider(
            modifier = Modifier
                .height(40.dp)
                .align(alignment = Alignment.CenterVertically),
            thickness = 1.dp,
        )
        CustomHorizontalSpacer(R.dimen.extra_margin)
        coinBalance?.let { BalanceItemSingleItem(header = stringResource(R.string.gift_total_coins), value = it) }
    }
}

@Composable
fun GenerosityScreen(modifier: Modifier=Modifier,generosityStatusSection: GenerosityStatusSection?=null,isPremium: Boolean=false,isVisiter:Boolean=false,actionDonate:()->Unit) {
    Column(
        modifier = modifier
            .background(colorResource(R.color.colorSurface), shape = RoundedCornerShape(8.dp))
    ) {
        Text(
            modifier = Modifier
                .padding(all = dimensionResource(id = R.dimen.activity_margin))
                .fillMaxWidth(),
            text = stringResource(R.string.title_generosoty_status),
            style = FlashatComposeTypography.defaultType.body1,
            color = colorResource(id = R.color.textColorPrimary)
        )
        Row(modifier = Modifier
            .padding(all = dimensionResource(id = R.dimen.element_spacing))
            .fillMaxWidth()) {
            GenerositySingleItem(modifier = Modifier
                .weight(weight = 1F)
                .fillMaxWidth(),icon = if(isPremium) R.drawable.ic_coins_gained else R.drawable.ic_coins_gained_free,generosityStatusSection?.coinsGainedFormatted, name = stringResource(R.string.gift_file_generosity_status_coins_gained)
            )
            VerticalDivider(
                modifier = Modifier
                    .height(120.dp)
                    .align(alignment = Alignment.CenterVertically),
                thickness = 1.dp,
                color = colorResource(R.color.colorDivider)
            )
            GenerositySingleItem(modifier = Modifier
                .weight(weight = 1F)
                .fillMaxWidth(),icon = if(isPremium)R.drawable.ic_coins_given else R.drawable.ic_coins_given_free,generosityStatusSection?.coinsGivenFormatted, name = stringResource(R.string.gift_file_generosity_status_coins_given)
            )

            VerticalDivider(
                modifier = Modifier
                    .height(120.dp)
                    .align(alignment = Alignment.CenterVertically),
                thickness = 1.dp,
                color = colorResource(R.color.colorDivider)
            )
            GenerositySingleItem(modifier = Modifier
                .weight(weight = 1F)
                .fillMaxWidth(),icon = if(isPremium) R.drawable.ic_give_away else R.drawable.ic_give_away_free,generosityStatusSection?.giveAwayPercentage?.toInt().toString()+"%", name = stringResource(R.string.gift_file_generosity_status_give_away)
            )
            if(!isVisiter) {
                VerticalDivider(
                    modifier = Modifier
                        .height(120.dp)
                        .align(alignment = Alignment.CenterVertically), thickness = 1.dp, color = colorResource(R.color.colorDivider)
                )
                GenerositySingleItem(
                    modifier = Modifier
                        .weight(weight = 1F)
                        .fillMaxWidth(),
                    icon = if(isPremium) R.drawable.ic_obligated_to_gift else R.drawable.ic_obligated_to_gift_free,
                    generosityStatusSection?.totalCoinObligationFormatted,
                    name = stringResource(R.string.gift_file_generosity_status_obligated_to_gift)
                )
            }
        }
        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.element_spacing))
        if(isPremium) {
            CustomLargeRoundButton(
                modifier = Modifier.padding(all = dimensionResource(id = R.dimen.element_spacing)), text = R.string.gift_file_donate, onClick = {actionDonate()}, isTextCaps = true)
        }
    }
}

@Composable
fun QuickActionScreen(flixBalance: String? = null, coinBalance: String? = null,actionBuyFlix:()-> Unit,actionBuyCoin:()-> Unit,actionConvert:()-> Unit,isPremium: Boolean?=false) {
    Column(
        modifier = Modifier
            .background(colorResource(R.color.colorSurface), shape = RoundedCornerShape(10.dp))
            .padding(all = dimensionResource(id = R.dimen.element_spacing)),
        verticalArrangement = Arrangement.spacedBy(dimensionResource(R.dimen.element_spacing))
    ) {
        flixBalance?.let {
            QuickActionSingleItem(modifier = Modifier, icon = R.drawable.ic_buy_flix, title = stringResource(R.string.title_buy_flix), subTitle = stringResource(R.string.gift_file_quick_actions_flix_Balance,it), actionClick = {actionBuyFlix()},isPremium = isPremium)
        }
        coinBalance?.let {
            QuickActionSingleItem(modifier = Modifier, icon = R.drawable.ic_coin, title = stringResource(R.string.title_buy_coins), subTitle = stringResource(R.string.gift_file_quick_actions_coin_Balance,it), actionClick = {actionBuyCoin()},isPremium = isPremium)
        }
        QuickActionSingleItem(modifier = Modifier, icon = R.drawable.ic_flax_to_coin, title = stringResource(R.string.gift_convert_flax_to_coins), actionClick = {actionConvert()},isPremium = isPremium )
    }
}

@Composable
fun ThisMonthGiftCountScreen(monthlySection: MonthlySection?=null,isPremium: Boolean=false) {
    Column(
        modifier = Modifier
            .background(colorResource(R.color.colorSurface), shape = RoundedCornerShape(8.dp))
            .padding(all = dimensionResource(id = R.dimen.element_spacing)),
        verticalArrangement = Arrangement.spacedBy(dimensionResource( R.dimen.element_spacing))
    ){
        val bgColor = colorResource(R.color.colorSurfaceSecondaryDark)
        val iconColor = colorResource(if(isPremium) R.color.colorPrimary else R.color.textColorAlwaysLightSecondaryLight).copy(alpha = 0.15f)

        GiftTotalCountSingleItem(modifier = Modifier, icon = if(isPremium)R.drawable.ic_gift_received else R.drawable.ic_gift_received_free, title = stringResource(R.string.gift_received), bgColor = bgColor, countColor = if(isPremium) R.color.colorPrimary else R.color.textColorAlwaysLightSecondaryLight, totalCount = monthlySection?.monthlyReceivedGifts?.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,iconBg = iconColor )
        GiftTotalCountSingleItem(modifier = Modifier, icon = if(isPremium) R.drawable.ic_gift_received_coins else R.drawable.ic_gift_received_coins_free , title = stringResource(R.string.gift_received_coins), bgColor = bgColor, countColor = if(isPremium) R.color.colorPrimary else R.color.textColorAlwaysLightSecondaryLight, totalCount = monthlySection?.monthlyCoinsReceivedGifts?.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,iconBg = iconColor)
        GiftTotalCountSingleItem(modifier = Modifier, icon = if(isPremium)R.drawable.ic_gift_received_flix else R.drawable.ic_gift_received_flix_free, title = stringResource(R.string.gift_received_flix), bgColor = bgColor, countColor = if(isPremium) R.color.colorPrimary else R.color.textColorAlwaysLightSecondaryLight, totalCount = monthlySection?.monthlyFlixReceivedGifts?.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,iconBg = iconColor)
        GiftTotalCountSingleItem(modifier = Modifier, icon = R.drawable.ic_coin_to_flax, title = stringResource(R.string.gift_converted_coin_to_flix), bgColor = bgColor, countColor = if(isPremium) R.color.colorPrimary else R.color.textColorAlwaysLightSecondaryLight, totalCount = monthlySection?.monthlyConvertedCoins?.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,iconBg = iconColor)
        GiftTotalCountSingleItem(modifier = Modifier, icon = if(isPremium)R.drawable.ic_gift_given else R.drawable.ic_gift_given_free, title = stringResource(R.string.gifts_given), bgColor = bgColor, countColor = if(isPremium) R.color.colorPrimary else R.color.textColorAlwaysLightSecondaryLight, totalCount = monthlySection?.monthlySentGifts?.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,iconBg = iconColor)
        GiftTotalCountSingleItem(modifier = Modifier, icon = R.drawable.ic_gift_given_coin , title = stringResource(R.string.gifts_given_coins), bgColor = bgColor, countColor = if(isPremium) R.color.colorPrimary else R.color.textColorAlwaysLightSecondaryLight, totalCount = monthlySection?.monthlyCoinsSentGifts.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,iconBg = iconColor)
        GiftTotalCountSingleItem(modifier = Modifier, icon = if(isPremium)R.drawable.ic_gift_away_percent else R.drawable.ic_gift_away_percent_free, title = stringResource(R.string.gift_given_away_percent), bgColor = bgColor, countColor = if(isPremium) R.color.colorPrimary else R.color.textColorAlwaysLightSecondaryLight, showProgressBar = true, progress = monthlySection?.monthlyProgress , totalCount = monthlySection?.monthlyGiveAwayPercentage.formatDecimalWithRemoveTrailingZeros()+"%", isPremium = isPremium,iconBg = iconColor)

    }
}

@Composable
fun TotalGiftCountScreen(totalSection: TotalSection?=null,isPremium: Boolean=false) {
    Column(
        modifier = Modifier
            .background(if (isPremium) colorResource(R.color.colorPrimary) else colorResource(R.color.textColorAlwaysLightSecondaryLight), shape = RoundedCornerShape(8.dp))
            .padding(all = dimensionResource(id = R.dimen.element_spacing)),
        verticalArrangement = Arrangement.spacedBy(dimensionResource( R.dimen.element_spacing))
    ){
        val bgColor = colorResource(R.color.colorAlwaysLightSurface).copy(alpha = 0.25f)
        val iconColor = colorResource(if(isPremium) R.color.colorSecondary else R.color.white).copy(alpha = 0.30f)

        GiftTotalCountSingleItem(modifier = Modifier, icon = if(isPremium)R.drawable.ic_gift_received_grant_total else R.drawable.ic_gift_received_grant_total_free, title = stringResource(R.string.gift_received), bgColor = bgColor, countColor =  if(isPremium) R.color.colorSecondary else R.color.white, totalCount = totalSection?.totalReceivedGifts?.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,isTotalSection = true,iconBg = iconColor)
        GiftTotalCountSingleItem(modifier = Modifier, icon = if(isPremium)R.drawable.ic_gift_received_coin_grant_total else R.drawable.ic_gift_received_coin_grant_total_free, title = stringResource(R.string.gift_received_coins), bgColor = bgColor, countColor = if(isPremium) R.color.colorSecondary else R.color.white, totalCount = totalSection?.totalCoinsReceivedGifts?.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,isTotalSection = true,iconBg = iconColor)
        GiftTotalCountSingleItem(modifier = Modifier, icon = if(isPremium)R.drawable.ic_gift_received_flix_grant_total else  R.drawable.ic_gift_received_flix_grant_total_free, title = stringResource(R.string.gift_received_flix),bgColor = bgColor,countColor = if(isPremium) R.color.colorSecondary else R.color.white, totalCount = totalSection?.totalFlixReceivedGifts?.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,isTotalSection = true,iconBg = iconColor)
        GiftTotalCountSingleItem(modifier = Modifier, icon = R.drawable.ic_coin_to_flax , title = stringResource(R.string.gift_converted_coin_to_flix),bgColor = bgColor,countColor = if(isPremium) R.color.colorSecondary else R.color.white, totalCount = totalSection?.totalConvertedCoins?.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,isTotalSection = true,iconBg = iconColor)
        GiftTotalCountSingleItem(modifier = Modifier, icon = if(isPremium)R.drawable.ic_gift_given_grant_total else R.drawable.ic_gift_given_grant_total_free, title = stringResource(R.string.gifts_given),bgColor = bgColor,countColor = if(isPremium) R.color.colorSecondary else R.color.white, totalCount = totalSection?.totalSentGifts?.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,isTotalSection = true,iconBg = iconColor)
        GiftTotalCountSingleItem(modifier = Modifier, icon = R.drawable.ic_gift_given_coin , title = stringResource(R.string.gifts_given_coins),bgColor = bgColor,countColor = if(isPremium) R.color.colorSecondary else R.color.white, totalCount = totalSection?.totalCoinsSentGifts?.formatDecimalWithRemoveTrailingZeros(), isPremium = isPremium,isTotalSection = true,iconBg = iconColor)
        GiftTotalCountSingleItem(modifier = Modifier, icon = if(isPremium)R.drawable.ic_gift_away_percent_grant_total else R.drawable.ic_gift_away_percent_grant_total_free, title = stringResource(R.string.gift_given_away_percent),bgColor = bgColor,countColor = if(isPremium) R.color.colorSecondary else R.color.white, showProgressBar = true,progress = totalSection?.totalProgress, totalCount = totalSection?.totalGiveAwayPercentage?.formatDecimalWithRemoveTrailingZeros()+"%", isPremium = isPremium,isTotalSection = true,iconBg = iconColor)

    }
}



/*******Single items*******/

@Composable
fun GiftTotalCountSingleItem(modifier: Modifier, @DrawableRes icon: Int, title:String,totalCount: String?, iconBg:Color,@ColorRes countColor: Int, bgColor: Color,  showProgressBar:Boolean?=false, progress:Double?=null,isPremium:Boolean=false,isTotalSection:Boolean=false){
    Row(modifier = modifier
        .background(color = bgColor, shape = RoundedCornerShape(8.dp))
        .heightIn(min = 60.dp)
        .fillMaxWidth()) {
        Box( modifier = modifier
            .padding(start = dimensionResource(id = R.dimen.element_spacing))
            .size(40.dp)
            .background(color = iconBg, shape = CircleShape)
            .align(alignment = Alignment.CenterVertically),
             contentAlignment = Alignment.Center){
            Icon(modifier = Modifier
                .size(24.dp),
                painter = painterResource(id = icon),
                contentDescription = null,
                tint =  Color.Unspecified
            )
        }
        CustomHorizontalSpacer(R.dimen.element_spacing)
            Text(
                modifier = modifier
                    .weight(1F)
                    .padding(all = dimensionResource(id = R.dimen.line_spacing))
                    .align(alignment = Alignment.CenterVertically)
                    .fillMaxWidth(),
                text = title,
                style = FlashatComposeTypography.defaultType.body2,

                color = when {
                    isTotalSection -> colorResource(R.color.white)
                    isPremium -> colorResource(R.color.textColorSecondary)
                    else -> colorResource(R.color.textColorAlwaysLightSecondaryLight)
                }

            )
        if(showProgressBar==true) {
            ProgressBarView(
                modifier = Modifier
                    .weight(0.3f)
                    .align(alignment = Alignment.CenterVertically), progress = progress?.toFloat(), color = colorResource(countColor)
            )
        }
        CustomHorizontalSpacer(R.dimen.element_spacing)
        Text(
            modifier = modifier
                .padding(all = dimensionResource(id = R.dimen.line_spacing))
                .align(alignment = Alignment.CenterVertically),
            text = totalCount?:"",
            style = FlashatComposeTypography.defaultType.h5,
            color = colorResource(id = countColor)
        )
    }
}

@Composable
fun QuickActionSingleItem(modifier: Modifier,@DrawableRes icon: Int,  title: String? ,subTitle: String?=null,actionClick:()-> Unit,isPremium: Boolean?=false) {
    Row(modifier = modifier
        .fillMaxWidth()
        .heightIn(min = 60.dp)
        .background(colorResource(R.color.colorSurfaceSecondaryDark), shape = RoundedCornerShape(8.dp))
        .padding(all = dimensionResource(id = R.dimen.line_spacing))
        .clickable { actionClick() },
        verticalAlignment = Alignment.CenterVertically
        ) {
        Icon(
            modifier = modifier
                .size(24.dp)
                .padding(start = dimensionResource(id = R.dimen.line_spacing))
                .align(alignment = Alignment.CenterVertically),
            painter = painterResource(id = icon),
            contentDescription = null,
            tint =  Color.Unspecified
        )
        CustomHorizontalSpacer(R.dimen.element_spacing)
        Column(modifier = modifier
            .weight(1F)
            .padding(all = dimensionResource(id = R.dimen.line_spacing))
            .fillMaxWidth()) {
            Text(
                text = title?:"",
                style = FlashatComposeTypography.defaultType.body1,
                color = if(isPremium == true) colorResource(id = R.color.colorPrimary) else colorResource(R.color.textColorAlwaysLightSecondaryLight)
            )
            if(subTitle!=null) {
                Text(
                    text = subTitle ?: "", style = FlashatComposeTypography.defaultType.overline, color = if(isPremium == true) colorResource(id = R.color.textColorPrimary) else colorResource(R.color.textColorAlwaysLightSecondaryLight)
                )
            }
        }
        Icon(
            modifier = modifier
                .align(alignment = Alignment.CenterVertically)
                .padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
                .size(size = dimensionResource(R.dimen.activity_margin)),
            painter = painterResource(id =  R.drawable.ic_caret_right),
            contentDescription = null,
            tint = colorResource(R.color.textColorSecondary)
        )
    }
}

@Composable
fun GenerositySingleItem(modifier: Modifier,@DrawableRes icon: Int,  totalCount: String? = null,name: String) {
    Column(modifier = modifier.padding(all = dimensionResource(id = R.dimen.line_spacing)),
           horizontalAlignment = Alignment.CenterHorizontally) {
        Icon(
            modifier = Modifier
                .size(size = dimensionResource(R.dimen.message_list_dp_size))
                .align(alignment = Alignment.CenterHorizontally),
            painter = painterResource(id = icon),
            contentDescription = null,
            tint =  Color.Unspecified
        )
        Text(
            modifier = Modifier
                .padding(all = dimensionResource(id = R.dimen.line_spacing))
                .align(alignment = Alignment.CenterHorizontally),
            text = totalCount?:"",
            style = FlashatComposeTypography.defaultType.subtitle2,
            color = colorResource(id = R.color.textColorPrimary)
        )
        Text(
            modifier = Modifier
                .padding(all = dimensionResource(id = R.dimen.line_spacing)),
            textAlign = TextAlign.Center,
            text = name,
            style = FlashatComposeTypography.defaultType.overline,
            color = colorResource(id = R.color.colorAlwaysLightSurfaceSecondaryDarker)
        )

    }
}


@Composable
fun BalanceItemSingleItem(header: String, value: String) {
    Column(modifier = Modifier.padding(all = dimensionResource(id = R.dimen.element_spacing))
        ) {
        Text(
            modifier = Modifier.padding(start = dimensionResource(id = R.dimen.line_spacing))
                .align(alignment = Alignment.CenterHorizontally),
            text = header,
            style = FlashatComposeTypography.defaultType.overline,
            color = colorResource(id = R.color.white)
        )
        Text(
            modifier = Modifier.padding(start = dimensionResource(id = R.dimen.line_spacing))
                .align(alignment = Alignment.CenterHorizontally),
            text = value,
            style = FlashatComposeTypography.defaultType.h5,
            color = colorResource(id = R.color.white)
        )
    }
}

@Composable
fun ProgressBarView(
    modifier: Modifier = Modifier,
    @FloatRange(from = 0.0, to = 1.0) progress: Float?,
    color: Color = colorResource(id = R.color.colorPrimaryLighter),
) {
    Log.d("PROGRESS","$progress")
    Row(
        modifier = modifier
            .height(height = dimensionResource(id = R.dimen.element_spacing))
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            .background(color = color.copy(alpha = 0.12f))
            .fillMaxWidth()
    ) {
        Box(
            modifier = Modifier
                .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
                .background(color = color)
                .fillMaxWidth(fraction = progress ?: 0F)
                .fillMaxHeight()
        )
    }
}


/*******Preview items*******/

@Preview(showBackground = true, uiMode = 33)
@Composable
private fun ViewTotalGiftCountScreen(){
    TotalGiftCountScreen()
}

@Preview(showBackground = true, uiMode = 33)
@Composable
private fun ViewThisMonthGiftCountScreen(){
    ThisMonthGiftCountScreen()
}

@Preview(showBackground = true, uiMode = 33)
@Composable
private fun ViewGiftTotalCountSingleItem() {
    val bgColor = colorResource(R.color.colorSurfaceSecondaryDark)
    GiftTotalCountSingleItem(modifier = Modifier,icon = R.drawable.ic_coins_gained, title = stringResource( R.string.title_buy_coins), totalCount = "40%", showProgressBar = true,countColor = R.color.colorPrimary, bgColor = bgColor , iconBg = colorResource(R.color.colorPrimary))
}

@Preview(showBackground = true, uiMode = 33)
@Composable
private fun ViewProgressItem() {
    ProgressBarView(progress = 0.1F)
}

@Preview(showBackground = true, uiMode = 33)
@Composable
private fun ViewQuickActionScreen() {
    QuickActionScreen(actionConvert = {}, actionBuyCoin = {}, actionBuyFlix = {})
}

@Preview(showBackground = true, uiMode = 33)
@Composable
private fun ViewQuickActionSingleScreen() {
    QuickActionSingleItem(modifier = Modifier, R.drawable.ic_coins_gained, title = stringResource( R.string.title_buy_coins), subTitle = "Balance : 200 FLiX", actionClick = {})
}

@Preview(showBackground = true, uiMode = 33)
@Composable
private fun ViewGenerosityScreen() {
    GenerosityScreen(actionDonate = {})
}

@Preview(showBackground = true, uiMode = 33)
@Composable
private fun ViewGenerositySingleItem() {
    GenerositySingleItem(modifier = Modifier.fillMaxWidth(),R.drawable.ic_coins_gained, totalCount = "200.32K", name = "COiNS Gained")
}


@Preview(showBackground = true, uiMode = 33)
@Composable
private fun ViewTotalBalanceScreen() {
    BalanceScreen()
}

@Preview(showBackground = true, uiMode = 33)
@Composable
private fun ViewBalanceSingleItemScreen() {
    BalanceItemSingleItem(header = stringResource(R.string.total_flax), value = "300000.0")
}

@Composable
 fun ShimmerGiftFileLayout(modifier: Modifier = Modifier, brush: Brush = Brush.linearGradient()) {
    Column(modifier = modifier.padding(all = dimensionResource(id = R.dimen.element_spacing))) {
        Row(modifier = modifier.fillMaxWidth(), horizontalArrangement = Arrangement.spacedBy(dimensionResource(id = R.dimen.element_spacing))) {
            repeat(4) {
                GenerosityShimmerComponent(modifier, brush)
            }
        }
        CustomVerticalSpacer(8.dp)
        Column {
            repeat(8) {
                ShimmerOtherComponent(modifier, brush)
            }
        }
    }


}

@Composable
private fun GenerosityShimmerComponent(modifier: Modifier, brush: Brush) {
    Column(
        modifier = modifier
            .width(80.dp)
            .clip(shape = RoundedCornerShape(dimensionResource(id = R.dimen.line_spacing)))
            .background(color = colorResource(R.color.colorSurfaceSecondaryDark))
            .padding(horizontal = dimensionResource(id = R.dimen.element_spacing)), horizontalAlignment = Alignment.CenterHorizontally
    ) {
        ShimmerDefaultCircleItem(brush = brush, size = dimensionResource(id = R.dimen.message_list_dp_size))

        CustomVerticalSpacer(8.dp)
        ShimmerDefaultItem(
            modifier = modifier.background(shape = RoundedCornerShape(dimensionResource(id = R.dimen.line_spacing)), color = colorResource(R.color.transparent)), brush = brush, fraction = 0.5f
        )

        CustomVerticalSpacer(8.dp)

        ShimmerDefaultItem(
            modifier = modifier.background(shape = RoundedCornerShape(dimensionResource(id = R.dimen.line_spacing)), color = colorResource(R.color.transparent)), brush = brush, fraction = 0.5f
        )
    }
}

@Composable
fun ShimmerOtherComponent(modifier: Modifier = Modifier,brush: Brush) {

    Row(modifier = modifier
        .fillMaxWidth()
        .height(60.dp)
        .clip(shape = RoundedCornerShape(dimensionResource(id = R.dimen.line_spacing)))
        .background(color = colorResource(R.color.colorSurfaceSecondaryDark))
        .padding(horizontal = dimensionResource(id = R.dimen.element_spacing)),
        verticalAlignment = Alignment.CenterVertically) {

        Column ( Modifier
                     .fillMaxWidth()
                     .weight(1f),
                 verticalArrangement = Arrangement.Center){

            ShimmerDefaultItem(
                modifier = modifier
                    .background(shape = RoundedCornerShape(dimensionResource(id = R.dimen.line_spacing)), color = colorResource(R.color.transparent))
                , brush = brush,
                fraction = 0.5f

            )
            CustomVerticalSpacer(8.dp)
            ShimmerDefaultItem(
                modifier = modifier
                    .background(shape = RoundedCornerShape(dimensionResource(id = R.dimen.line_spacing)), color = colorResource(R.color.transparent))
                , brush = brush,

                )
        }
    }


}